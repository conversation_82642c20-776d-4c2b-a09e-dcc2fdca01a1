"use client";

import React, { useState } from 'react';
import { useAuth } from '@/context/auth-context';
import { supabase } from '@/integrations/supabase/client';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { FileUploader } from '@/components/Vault/FileUploader';
import { encryptFile, arrayBufferToBase64 } from '@/utils/encryption';
import { toast } from 'sonner';
import { Loader2, Lock } from 'lucide-react';

const documentCategories = [
  { value: 'will', label: 'Will' },
  { value: 'trust', label: 'Trust' },
  { value: 'financial', label: 'Financial' },
  { value: 'medical', label: 'Medical' },
  { value: 'insurance', label: 'Insurance' },
  { value: 'property', label: 'Property' },
  { value: 'digital', label: 'Digital' },
  { value: 'other', label: 'Other' },
];

interface DocumentFormProps {
  onSuccess: () => void;
}

export default function DocumentForm({ onSuccess }: DocumentFormProps) {
  const [documentName, setDocumentName] = useState('');
  const [category, setCategory] = useState('');
  const [file, setFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const { user } = useAuth();

  const resetForm = () => {
    setDocumentName('');
    setCategory('');
    setFile(null);
  };

  const handleUpload = async () => {
    if (!file || !documentName || !category || !user) {
      toast.error('Please fill in all fields and select a file');
      return;
    }

    try {
      setIsUploading(true);

      // Generate a random encryption key
      const encryptionKey = crypto.getRandomValues(new Uint8Array(32));
      const encryptionKeyBase64 = arrayBufferToBase64(encryptionKey.buffer);

      // Encrypt the file
      const encryptedFile = await encryptFile(file);

      // Convert the encrypted file to base64 for API transmission
      const reader = new FileReader();
      const fileBase64Promise = new Promise<string>((resolve) => {
        reader.onloadend = () => {
          const base64data = reader.result as string;
          // Remove the data URL prefix (e.g., "data:application/pdf;base64,")
          const base64Content = base64data.split(',')[1];
          resolve(base64Content);
        };
      });
      reader.readAsDataURL(encryptedFile);
      const fileBase64 = await fileBase64Promise;

      // Use the API route to upload the file and create the document record
      const response = await fetch('/api/documents/upload', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: documentName,
          category,
          encryptedFile: {
            name: file.name,
            data: fileBase64
          },
          encryptionKey: encryptionKeyBase64,
          fileType: file.type,
          fileSize: file.size
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Upload failed with status ${response.status}`);
      }

      const data = await response.json();

      toast.success('Document uploaded successfully and securely encrypted');
      resetForm();
      onSuccess();
    } catch (error: any) {
      console.error('Upload error:', error);
      toast.error(`Upload failed: ${error.message}`);
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className="space-y-4">
      <div>
        <Label htmlFor="documentName">Document Name</Label>
        <Input
          type="text"
          id="documentName"
          placeholder="Document name"
          value={documentName}
          onChange={(e) => {
            // Ensure first letter is capitalized
            const value = e.target.value;
            if (value.length > 0) {
              const capitalized = value.charAt(0).toUpperCase() + value.slice(1);
              setDocumentName(capitalized);
            } else {
              setDocumentName(value);
            }
          }}
        />
      </div>

      <div>
        <Label htmlFor="category">Category</Label>
        <Select onValueChange={setCategory} value={category}>
          <SelectTrigger>
            <SelectValue placeholder="Select a category" />
          </SelectTrigger>
          <SelectContent>
            {documentCategories.map((cat) => (
              <SelectItem key={cat.value} value={cat.value}>
                {cat.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div>
        <Label>File</Label>
        <FileUploader
          onFileSelected={(selectedFile: File) => setFile(selectedFile)}
          maxSizeMB={5120} // 5GB
          acceptedFileTypes={[
            'application/pdf', '.pdf',
            'application/msword', '.doc',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document', '.docx',
            'application/vnd.ms-excel', '.xls',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', '.xlsx',
            'application/vnd.ms-powerpoint', '.ppt',
            'application/vnd.openxmlformats-officedocument.presentationml.presentation', '.pptx',
            'image/jpeg', '.jpg', '.jpeg',
            'image/png', '.png',
            'image/gif', '.gif',
            'image/tiff', '.tif', '.tiff',
            'application/zip', '.zip',
            'application/x-rar-compressed', '.rar',
            'text/plain', '.txt',
            'text/csv', '.csv',
            'application/json', '.json',
            'application/xml', '.xml',
            'video/mp4', '.mp4',
            'video/quicktime', '.mov',
            'audio/mpeg', '.mp3',
            'audio/wav', '.wav'
          ]}
        />
      </div>

      <div className="bg-blue-50 p-4 rounded-md border border-blue-100">
        <p className="text-xs text-blue-700">
          <strong>Maximum file size:</strong> 5GB<br />
          <strong>Supported formats:</strong> PDF, Word, Excel, PowerPoint, images, videos, audio, text files, and more.
        </p>
      </div>

      <Button
        onClick={handleUpload}
        disabled={!file || !documentName || !category || isUploading}
        className="w-full"
      >
        {isUploading ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Uploading...
          </>
        ) : (
          'Upload Document'
        )}
      </Button>
    </div>
  );
}
